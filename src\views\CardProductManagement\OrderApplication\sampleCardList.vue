<template>
  <UmvContent>
    <!-- 查询 -->
    <UmvQuery
      :col-length-map="{
        2400: 6, // 屏幕宽度 >= 2560px 时使用 8 列
        1900: 5, // 屏幕宽度 >= 1920px 时使用 8 列
        1600: 4, // 屏幕宽度 >= 1600px 且 < 1920px 时使用 6 列
        1280: 4, // 屏幕宽度 >= 1280px 且 < 1600px 时使用 5 列
        1100: 3, // 屏幕宽度 >= 1280px 且 < 1600px 时使用 5 列
        1000: 2, // 屏幕宽度 >= 1000px 且 < 1280px 时使用 3 列
        768: 2, // 屏幕宽度 >= 768px 且 < 1000px 时使用 2 列
        0: 1 // 屏幕宽度 < 768px 时使用 1 列
      }"
      v-model="queryForm"
      :opts="queryOpts"
      @check="onSearch"
      @reset="onReset"
    />
    <!-- 列表 -->
    <UmvTable
      v-loading="loading"
      :data="datas"
      :columns="columns"
      :header-cell-style="{ background: '#F7F7F9', color: '#606266' }"
      @refresh="searchSamples"
    >
      <!-- 工具栏 -->
      <template #tools>
        <el-button type="primary" size="small" v-track:click.btn @click="onAdd">新增</el-button>
      </template>

      <!-- 分页组件 -->
      <template #pagination>
        <Pagination
          layout="prev, pager, next, sizes, jumper"
          :total="total"
          v-model:current-page="pageIndex"
          v-model:page-size="pageSize"
          @change="searchSamples"
        />
      </template>
    </UmvTable>
  </UmvContent>
</template>

<script setup lang="tsx">
defineOptions({
  name: 'sampleCardList'
})

import { View as IconView } from '@element-plus/icons-vue'
import { ElInput, ElSelect, ElOption, ElButton, ElLink, ElIcon } from 'element-plus'
import UmvContent from '@/components/UmvContent'
import { UmvQuery, type QueryOption, type QueryForm } from '@/components/UmvQuery'
import UmvTable from '@/components/UmvTable'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { CustomerSelect } from '@/components/CustomerSelect'
import IOrderApplication from '@/api/orderApplication/types/orderApplication'
import { sampleCardApplicationTypeArray, orderApplicationStatusArray } from './types/data.d'
import { useOrderAppliactionListService } from './hooks/useOrderApplicationListService'
import { useOrderApplicationCommonService } from './hooks/useOrderApplicationCommonService'
import {
  orderApplicationReviewResultEnum,
  orderApplicationStatusEnum,
  orderApplicationTypeEnum
} from '@/api/orderApplication/types/enum.d'
const router = useRouter()
const orderApplicationService = useOrderAppliactionListService()
const { params, searchSamples, pageIndex, pageSize, resetParmas, datas, total } =
  orderApplicationService //查询条件

const commonService = useOrderApplicationCommonService()
const { applicationTypeMapper } = commonService

const loading = ref()

// 查询表单数据
const queryForm = ref({
  applyCode: '',
  customerName: '',
  type: '' as orderApplicationTypeEnum | '',
  status: '' as orderApplicationStatusEnum | ''
})

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  applyCode: {
    label: '申请单号',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput v-model={form.applyCode} placeholder="申请单号" clearable style="width: 100%" />
    )
  },
  customerName: {
    label: '客户名称',
    defaultVal: '',
    controlRender: (form: any) => (
      <CustomerSelect
        v-model:customer-name={form.customerName}
        isSearchByName
        clearable
        style="width: 100%"
      />
    )
  },
  type: {
    label: '申请类型',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.type} placeholder="请选择类型" clearable style="width: 100%">
        {sampleCardApplicationTypeArray.map((item) => (
          <ElOption key={item.value} label={item.label} value={item.value} />
        ))}
      </ElSelect>
    )
  },
  status: {
    label: '申请状态',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.status} placeholder="请选择状态" clearable style="width: 100%">
        {orderApplicationStatusArray.map((item) => (
          <ElOption key={item.value} label={item.label} value={item.value} />
        ))}
      </ElSelect>
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'applyCode',
    label: '申请单编号',
    width: '180px',
    renderTemplate: (scope) => (
      <div>
        <ElLink type="primary" underline={false} onClick={() => onView(scope.row)}>
          {scope.row.applyCode}
        </ElLink>
        {scope.row.umvOrderId && (
          <ElLink
            type="danger"
            underline={false}
            title="订单详情"
            onClick={() => onGoOrder(scope.row)}
          >
            <ElIcon class="el-icon--right">
              <IconView />
            </ElIcon>
          </ElLink>
        )}
      </div>
    )
  },
  { prop: 'customerName', label: '客户名称' },
  {
    prop: 'type',
    label: '申请类型',
    width: '180px',
    renderTemplate: (scope) => (
      <span
        class="badge"
        style={{
          background:
            scope.row.type === orderApplicationTypeEnum.FreeSampleCard
              ? 'rgb(252, 179, 34)'
              : 'rgb(128, 117, 196)'
        }}
      >
        {applicationTypeMapper(scope.row.type)}
      </span>
    )
  },
  { prop: 'createName', label: '申请人' },
  { prop: 'saleUserName', label: '区域销售', width: '180px' },
  {
    prop: 'managerName',
    label: '区域负责人',
    width: '180px',
    renderTemplate: (scope) => (
      <div>
        {scope.row.managerName}
        {isNG(scope.row.managerResult) && (
          <span class="badge" style={{ background: 'rgb(245, 108, 108)' }}>
            NG
          </span>
        )}
      </div>
    )
  },
  {
    prop: 'status',
    label: '申请状态',
    width: '180px',
    renderTemplate: (scope) => (
      <span class="badge" style={{ background: statusColorMapper(scope.row.status) }}>
        {dispalyStatus(scope.row.status)}
      </span>
    )
  },
  { prop: 'createDate', label: '创建时间' },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: '200px',
    renderTemplate: (scope) => (
      <div>
        <ElButton type="primary" link onClick={() => onView(scope.row)}>
          查看
        </ElButton>
        {isEditable(scope.row.status) && (
          <ElButton type="primary" link onClick={() => onEdit(scope.row)}>
            编辑
          </ElButton>
        )}
      </div>
    )
  }
])

//判断是否可以编辑
function isEditable(status: orderApplicationStatusEnum): boolean {
  return status != orderApplicationStatusEnum.FINISH && status != orderApplicationStatusEnum.CANCEL
}

//显示申请类型名称
function dispalyStatus(value: orderApplicationStatusEnum): string {
  const result = orderApplicationStatusArray.filter((item) => item.value === value)[0]
  if (result) {
    return result.label
  }
  return ''
}
//显示申请状态对应的颜色
function statusColorMapper(value: orderApplicationStatusEnum): string {
  const result = orderApplicationStatusArray.filter((item) => item.value == value)[0]
  if (result) {
    return result.color
  }
  return ''
}
//评审是否NT
function isNG(value: orderApplicationReviewResultEnum) {
  return value == orderApplicationReviewResultEnum.NT
}

function onSearch() {
  // 将查询表单数据同步到params
  params.value.applyCode = queryForm.value.applyCode
  params.value.customerName = queryForm.value.customerName
  params.value.type = queryForm.value.type || undefined
  params.value.status = queryForm.value.status || undefined

  pageIndex.value = 1
  searchSamples()
}

function onReset() {
  // 重置查询表单数据
  queryForm.value = {
    applyCode: '',
    customerName: '',
    type: '',
    status: ''
  }
  resetParmas()
}

function onAdd() {
  router.push({
    name: `SampleCardEdit`
  })
}

function onView(data: IOrderApplication) {
  const id: string = data.id
  router.push({
    name: `SampleCardView`,
    query: {
      id
    }
  })
}

function onEdit(data: IOrderApplication) {
  const id: string = data.id
  router.push({
    name: `SampleCardEdit`,
    query: {
      id
    }
  })
}

function onGoOrder(data: IOrderApplication) {
  if (!data.umvOrderId) {
    return
  }
  router.push({
    name: 'BatchCardDetail',
    query: { orderId: data.umvOrderId }
  })
}

// 初始化查询表单数据
watchEffect(() => {
  queryForm.value.applyCode = params.value.applyCode || ''
  queryForm.value.customerName = params.value.customerName || ''
  queryForm.value.type = params.value.type || ''
  queryForm.value.status = params.value.status || ''
})

onMounted(() => {
  nextTick(() => {
    onSearch()
  })
})

onActivated(() => searchSamples())
</script>

<style scoped lang="less">
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999;
  border-radius: 10px;
}
</style>
